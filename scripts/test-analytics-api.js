const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testAnalyticsAPI() {
  console.log('🧪 Testing Analytics Dashboard API Endpoints...\n');

  const endpoints = [
    '/api/admin/reports/overview',
    '/api/admin/reports/routes?period=current',
    '/api/admin/reports/revenue?period=monthly',
    '/api/admin/reports/trends?period=monthly',
    '/api/admin/reports/benchmarks?period=current'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing: ${endpoint}`);
      const response = await fetch(`${BASE_URL}${endpoint}`);
      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`✅ Success: ${endpoint}`);
        console.log(`   Data keys: ${Object.keys(data.data).join(', ')}`);
      } else {
        console.log(`❌ Failed: ${endpoint}`);
        console.log(`   Error: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ Error: ${endpoint}`);
      console.log(`   ${error.message}`);
    }
    console.log('');
  }

  console.log('🎉 Analytics API testing completed!');
}

// Run the test
testAnalyticsAPI().catch(console.error); 