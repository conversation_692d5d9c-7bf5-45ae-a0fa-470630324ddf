/*
  # Add Current Revenue Tracking to Admin Settings
  
  This migration adds a current_revenue column to the admin_settings table to track
  total revenue from bookings. The column will be automatically updated by the
  handle_new_booking() trigger function and reset by the reset_all_bookings() function.
  
  Changes:
  1. Add current_revenue column to admin_settings table
  2. Update handle_new_booking() function to increment revenue
  3. Update reset_all_bookings() function to reset revenue
  4. Update get_detailed_booking_statistics() function to include revenue
  5. Calculate and populate initial revenue from existing bookings
*/

-- Add current_revenue column to admin_settings table
ALTER TABLE admin_settings 
ADD COLUMN IF NOT EXISTS current_revenue DECIMAL(10,2) DEFAULT 0.00 NOT NULL CHECK (current_revenue >= 0);

-- Initialize current_revenue to 0 if it doesn't exist
UPDATE admin_settings 
SET current_revenue = 0.00 
WHERE current_revenue IS NULL;

-- Calculate and populate initial revenue from existing bookings
UPDATE admin_settings 
SET current_revenue = COALESCE(
  (SELECT SUM(COALESCE(fare, 0)) FROM bookings WHERE payment_status = true), 
  0.00
)
WHERE id = 1;

-- Update handle_new_booking() function to increment revenue
CREATE OR REPLACE FUNCTION handle_new_booking()
RETURNS TRIGGER AS $$
BEGIN
  -- Increment current_bookings by 1
  UPDATE admin_settings 
  SET current_bookings = COALESCE(current_bookings, 0) + 1,
      updated_at = NOW()
  WHERE id = 1;
  
  -- Update paid/unpaid bookings based on payment status
  IF NEW.payment_status = true THEN
    UPDATE admin_settings 
    SET paid_bookings = COALESCE(paid_bookings, 0) + 1,
        current_revenue = COALESCE(current_revenue, 0.00) + COALESCE(NEW.fare, 0.00),
        updated_at = NOW()
    WHERE id = 1;
  ELSE
    UPDATE admin_settings 
    SET unpaid_bookings = COALESCE(unpaid_bookings, 0) + 1,
        updated_at = NOW()
    WHERE id = 1;
  END IF;
  
  -- Decrement available_seats for the specific bus route
  UPDATE buses 
  SET available_seats = GREATEST(COALESCE(available_seats, 0) - 1, 0),
      updated_at = NOW()
  WHERE route_code = NEW.bus_route AND is_active = true;
  
  -- Log the booking for debugging (optional)
  RAISE NOTICE 'New booking processed: Student %, Route %, Payment Status %, Fare %', 
    NEW.student_name, NEW.bus_route, NEW.payment_status, COALESCE(NEW.fare, 0.00);
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the booking insertion
    RAISE WARNING 'Error in handle_new_booking trigger: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update reset_all_bookings() function to reset revenue and seats
CREATE OR REPLACE FUNCTION reset_all_bookings()
RETURNS VOID AS $$
BEGIN
  -- Archive current booking round before resetting (if function exists)
  BEGIN
    PERFORM archive_booking_round();
  EXCEPTION
    WHEN undefined_function THEN
      RAISE NOTICE 'archive_booking_round() function not found, skipping archival';
    WHEN OTHERS THEN
      RAISE NOTICE 'Error archiving booking round: %', SQLERRM;
  END;

  -- Reset available seats for all active buses to their total seats
  UPDATE buses
  SET available_seats = total_seats,
      updated_at = NOW()
  WHERE is_active = true;

  -- Reset booking statistics in admin_settings
  UPDATE admin_settings
  SET current_bookings = 0,
      paid_bookings = 0,
      unpaid_bookings = 0,
      current_revenue = 0.00,
      updated_at = NOW()
  WHERE id = 1;

  -- Reset analytics_revenue table (if it exists)
  BEGIN
    DELETE FROM analytics_revenue;
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'analytics_revenue table not found, skipping deletion';
    WHEN OTHERS THEN
      RAISE NOTICE 'Error clearing analytics_revenue: %', SQLERRM;
  END;

  -- Log the reset operation
  RAISE NOTICE 'All booking statistics, bus seats, revenue, and analytics have been reset successfully.';
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error resetting bookings: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing function before recreating with new return type
DROP FUNCTION IF EXISTS get_detailed_booking_statistics();

-- Update get_detailed_booking_statistics() function to include revenue
CREATE OR REPLACE FUNCTION get_detailed_booking_statistics()
RETURNS TABLE(
  total_buses BIGINT,
  total_bookings BIGINT,
  current_bookings INTEGER,
  paid_bookings INTEGER,
  unpaid_bookings INTEGER,
  current_revenue DECIMAL(10,2),
  available_seats BIGINT,
  total_capacity BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM buses WHERE is_active = true) as total_buses,
    (SELECT COUNT(*) FROM bookings) as total_bookings,
    (SELECT COALESCE(admin_settings.current_bookings, 0) FROM admin_settings WHERE id = 1) as current_bookings,
    (SELECT COALESCE(admin_settings.paid_bookings, 0) FROM admin_settings WHERE id = 1) as paid_bookings,
    (SELECT COALESCE(admin_settings.unpaid_bookings, 0) FROM admin_settings WHERE id = 1) as unpaid_bookings,
    (SELECT COALESCE(admin_settings.current_revenue, 0.00) FROM admin_settings WHERE id = 1) as current_revenue,
    (SELECT COALESCE(SUM(available_seats), 0) FROM buses WHERE is_active = true) as available_seats,
    (SELECT COUNT(*) * 50 FROM buses WHERE is_active = true) as total_capacity; -- Assuming 50 seats per bus
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;