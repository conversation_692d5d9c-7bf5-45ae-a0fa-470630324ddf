'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Bus, Users, AlertTriangle } from 'lucide-react';
import type { RoutesTabProps, OccupancyColor } from '@/types/reports';

const RoutesTab: React.FC<RoutesTabProps> = ({ data, isLoading, error, onRefresh }) => {
  // Get occupancy color
  const getOccupancyColor = (percentage: number): OccupancyColor => {
    if (percentage <= 60) return 'green';
    if (percentage <= 85) return 'yellow';
    return 'red';
  };

  // Get progress bar color class
  const getProgressColor = (percentage: number): string => {
    const color = getOccupancyColor(percentage);
    switch (color) {
      case 'green': return 'bg-green-500';
      case 'yellow': return 'bg-yellow-500';
      case 'red': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  // Get status icon and color
  const getStatusIcon = (percentage: number) => {
    const color = getOccupancyColor(percentage);
    switch (color) {
      case 'green':
        return <Users className="w-5 h-5 text-green-600" />;
      case 'yellow':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'red':
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
      default:
        return <Users className="w-5 h-5 text-gray-600" />;
    }
  };

  // Sort routes by occupancy percentage (highest first)
  const sortedRoutes = data?.routes.sort((a, b) => b.occupancyPercentage - a.occupancyPercentage) || [];

  // Error state
  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={onRefresh} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-32 w-full" />
        ))}
      </div>
    );
  }

  // Empty state
  if (!data?.routes || data.routes.length === 0) {
    return (
      <div className="text-center py-8">
        <Bus className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500 text-lg">No route data available</p>
        <Button onClick={onRefresh} variant="outline" className="mt-4">
          Refresh Data
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Bus className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Routes</p>
                <p className="text-2xl font-bold">{data.routes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Users className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Bookings</p>
                <p className="text-2xl font-bold">
                  {data.routes.reduce((sum, route) => sum + route.currentBookings, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">High Occupancy</p>
                <p className="text-2xl font-bold">
                  {data.routes.filter(route => route.occupancyPercentage > 85).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Routes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sortedRoutes.map((route) => {
          const occupancyColor = getOccupancyColor(route.occupancyPercentage);
          
          return (
            <Card 
              key={route.routeCode} 
              className={`transition-all duration-200 hover:shadow-lg ${
                occupancyColor === 'red' ? 'border-red-200 bg-red-50' :
                occupancyColor === 'yellow' ? 'border-yellow-200 bg-yellow-50' :
                'border-green-200 bg-green-50'
              }`}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-lg">
                  <span className="truncate">{route.busName}</span>
                  {getStatusIcon(route.occupancyPercentage)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Occupancy Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Occupancy</span>
                    <span className="font-semibold">{route.occupancyPercentage}%</span>
                  </div>
                  <div className="relative">
                    <Progress 
                      value={route.occupancyPercentage} 
                      className="h-3"
                    />
                    <div 
                      className={`absolute top-0 left-0 h-3 rounded-full transition-all duration-300 ${getProgressColor(route.occupancyPercentage)}`}
                      style={{ width: `${Math.min(route.occupancyPercentage, 100)}%` }}
                    />
                  </div>
                </div>

                {/* Booking Details */}
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">
                    {route.currentBookings} of {route.totalSeats} seats booked
                  </span>
                </div>

                {/* Status Badge */}
                <div className="flex justify-center">
                  <span 
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      occupancyColor === 'red' ? 'bg-red-100 text-red-800' :
                      occupancyColor === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}
                  >
                    {occupancyColor === 'red' ? 'High Demand' :
                     occupancyColor === 'yellow' ? 'Moderate Demand' :
                     'Available'}
                  </span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Occupancy Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-sm">0-60%: Available</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-yellow-500 rounded"></div>
              <span className="text-sm">61-85%: Moderate Demand</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-sm">86-100%: High Demand</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoutesTab;
