import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/middleware';
import { supabaseAdmin } from '@/lib/supabase';
import type { RoutesApiResponse } from '@/types/reports';

export async function GET(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      // Get bus data with current bookings
      const { data: busData, error: busError } = await supabaseAdmin
        .from('buses')
        .select(`
          route_code,
          name,
          total_seats,
          available_seats,
          is_active
        `)
        .eq('is_active', true)
        .order('name');

      if (busError) {
        console.error('Error fetching bus data:', busError);
        return NextResponse.json({
          success: false,
          error: 'Failed to fetch bus data',
          data: null
        }, { status: 500 });
      }

      // Transform data and calculate occupancy
      const routes = busData?.map(bus => {
        const currentBookings = (bus.total_seats || 50) - (bus.available_seats || 0);
        const totalSeats = bus.total_seats || 50;
        const occupancyPercentage = totalSeats > 0 ? Math.round((currentBookings / totalSeats) * 100) : 0;

        return {
          routeCode: bus.route_code,
          busName: bus.name,
          currentBookings: Math.max(0, currentBookings), // Ensure non-negative
          totalSeats,
          occupancyPercentage: Math.min(100, Math.max(0, occupancyPercentage)), // Clamp between 0-100
          isActive: bus.is_active
        };
      }) || [];

      // Sort by occupancy percentage (highest first)
      routes.sort((a, b) => b.occupancyPercentage - a.occupancyPercentage);

      const response: RoutesApiResponse = {
        success: true,
        data: {
          routes
        }
      };

      return NextResponse.json(response, {
        headers: {
          'Cache-Control': 'max-age=300', // 5-minute cache
          'Content-Type': 'application/json'
        }
      });

    } catch (error) {
      console.error('Unexpected error in routes endpoint:', error);
      return NextResponse.json({
        success: false,
        error: 'Internal server error',
        data: null
      }, { status: 500 });
    }
  });
}
